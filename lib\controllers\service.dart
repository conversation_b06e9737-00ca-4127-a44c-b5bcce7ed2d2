// Core Dart imports
import 'dart:async' show Future, Timer, TimeoutException;
import 'dart:convert';
import 'dart:io';

// External package imports
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:media_kit/media_kit.dart'; // For Media class
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as p;
import 'package:shelf/shelf.dart' as shelf;
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_cors_headers/shelf_cors_headers.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart' as shelf_web_socket;

// Internal imports
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';

// ============================================================================
// HELPER FUNCTIONS AND UTILITIES
// ============================================================================

/// Helper function to generate consistent timestamps
String _generateTimestamp() => DateTime.now().toIso8601String();

/// Helper function to navigate to a route if not already there
Future<void> _navigateToRouteIfNeeded(String targetRoute) async {
  final currentRoute = Get.currentRoute;
  if (currentRoute.isNotEmpty && currentRoute != targetRoute) {
    await Get.toNamed(targetRoute);
  }
}

/// Helper function to create standard WebSocket success response
Map<String, dynamic> _createWebSocketResponse({
  required String status,
  required String action,
  String? message,
  Map<String, dynamic>? additionalData,
}) {
  final response = <String, dynamic>{
    'status': status,
    'action': action,
  };

  if (message != null) response['message'] = message;
  if (additionalData != null) response.addAll(additionalData);

  return response;
}



// ============================================================================
// MAIN FUNCTIONALITY
// ============================================================================

/// Process URL from video with timestamp
Future<String> _processVideoUrl(String videoUrl, StorageManager storage) async {
  if (storage.read(StorageBox.videoNotes, VideoNotesStorageKeys.useRelativePath) ?? false) {
    final rootDir = storage.read(StorageBox.videoNotes, VideoNotesStorageKeys.rootDir) ?? "";
    if (videoUrl.isNotEmpty && !videoUrl.startsWith('http')) {
      videoUrl = p.join(rootDir, videoUrl);
    }
  }

  // URL decoding if needed
  final usePathEncoding = storage.read(StorageBox.videoNotes, VideoNotesStorageKeys.usePathEncoding, true);
  if (usePathEncoding) {
    try {
      videoUrl = Uri.decodeComponent(videoUrl);
      logger.d('URL解码后: $videoUrl');
    } catch (e) {
      logger.w('URL解码失败，使用原始URL: $e');
    }
  }

  return videoUrl;
}

/// Parse timestamp from query parameters
int _parseTimestamp(String? timeStr) {
  if (timeStr == null) return 0;

  // Check if it's time format (contains :)
  if (timeStr.contains(':')) {
    final parts = timeStr.split(':').map(int.parse).toList();
    if (parts.length == 2) {
      // mm:ss format
      return parts[0] * 60 + parts[1];
    } else if (parts.length == 3) {
      // hh:mm:ss format
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
  } else {
    // Pure number format (seconds)
    return int.parse(timeStr);
  }
  return 0;
}

/// Handle video link processing with browser or built-in player
Future<void> _handleVideoLink(String videoUrl, int timestamp, VideoNoteController videoNoteController) async {
  final defaultPlayer = videoNoteController.defaultPlayer.value;
  logger.i('默认播放器设置: $defaultPlayer');

  if (defaultPlayer == "browser" && videoUrl.startsWith('http')) {
    await _handleBrowserVideoLink(videoUrl, timestamp);
  } else {
    await _handleBuiltInVideoLink(videoUrl, timestamp, videoNoteController);
  }
}

/// Handle video link with browser player
Future<void> _handleBrowserVideoLink(String videoUrl, int timestamp) async {
  logger.i('使用浏览器模式处理时间戳链接: $videoUrl, 时间戳: $timestamp秒');

  try {
    final wsManager = Get.find<WebSocketManager>();

    // Check WebSocket connection status with proper waiting logic
    if (!wsManager.isBrowserExtensionAvailable()) {
      logger.i('立即检查失败，等待浏览器扩展连接...');

      final waitResult = await wsManager.waitForBrowserExtension(
        timeout: const Duration(seconds: 2)
      );

      if (!waitResult) {
        logger.w('WebSocket未连接或浏览器扩展不可用，显示连接提示');
        showLocalNotification(
          'service.browser.extensionRequired'.tr,
          'service.browser.extensionInstallPrompt'.tr
        );
        return;
      }
    }

    // Send browser navigation command
    wsManager.navigateToVideoTimestamp(videoUrl, timestamp);
    logger.i('已发送浏览器智能导航命令: $videoUrl, 时间戳: $timestamp秒');

    // Show success notification
    showLocalNotification(
      'service.browser.openingVideo'.tr,
      'service.browser.videoWillOpen'.tr
    );

  } catch (e) {
    logger.e('浏览器模式处理失败: $e');
    showLocalNotification(
      'service.browser.navigationFailed'.tr,
      'service.browser.cannotOpenVideo'.tr
    );
  }
}

/// Handle video link with built-in player
Future<void> _handleBuiltInVideoLink(String videoUrl, int timestamp, VideoNoteController videoNoteController) async {
  logger.i('使用内置播放器模式处理时间戳链接: $videoUrl, 时间戳: $timestamp秒');

  if (videoUrl.isNotEmpty) {
    videoNoteController.addToPlaylist(videoUrl);
  }

  if (videoNoteController.getCurrentVideoUrl() != videoUrl) {
    await videoNoteController.player.value.open(Media(videoUrl));
  }
  if (timestamp > 0) {
    await videoNoteController.player.value.seek(Duration(seconds: timestamp));
  }
}

Future<void> processLink(Uri uri) async {
  final settingController = Get.find<SettingController>();
  final videoNoteController = Get.find<VideoNoteController>();
  final storage = StorageManager();

  logger.d('Current route: ${Get.currentRoute}');
  logger.d('onAppLink: $uri');

  final queryParams = uri.queryParameters;
  logger.i('queryParams: $queryParams');
  final category = queryParams['category'] ?? "video";

  if (category == "video") {
    await _navigateToRouteIfNeeded('/video_note');

    if (queryParams.containsKey('path')) {
      var videoUrl = queryParams['path']!;
      logger.d('deep link videoUrl: $videoUrl');
      logger.d('current videoUrl: ${videoNoteController.getCurrentVideoUrl()}');

      // Process video URL (handle relative paths and encoding)
      videoUrl = await _processVideoUrl(videoUrl, storage);

      // Parse timestamp
      final timestamp = _parseTimestamp(queryParams['t']);
      logger.d('t: $timestamp seconds');

      // Handle video link with appropriate player
      await _handleVideoLink(videoUrl, timestamp, videoNoteController);
    }
  } else if (category == "pdf") {
    await _handlePdfLink(queryParams, settingController);
  } else if (category == "zotero") {
    final path = queryParams['path']!;
    openFileBySystemDefault(path);
  } else {
    final path = queryParams['path']!;
    openFileBySystemDefault(path);
  }
}

/// Handle PDF link opening with appropriate reader
Future<void> _handlePdfLink(Map<String, String> queryParams, SettingController settingController) async {
  final path = queryParams['path']!;
  final page = queryParams['page'] ?? "1";

  logger.i('Handling PDF link: path=$path, page=$page');

  // Validate path parameter
  if (path.isEmpty) {
    logger.e('PDF path is empty in query parameters');
    return;
  }

  // If no custom PDF reader is configured, use system default
  if (settingController.pdfReaderPath.value.isEmpty) {
    logger.i('No custom PDF reader configured, using system default');
    try {
      await openFileBySystemDefault(path);
      logger.i('PDF opened successfully with system default');
    } catch (e) {
      logger.e('Failed to open PDF with system default: $e');
    }
    return;
  }

  final appName = PathUtils(settingController.pdfReaderPath.value).stem.toLowerCase();
  final readerPath = settingController.pdfReaderPath.value;

  logger.i('Using custom PDF reader: $appName at $readerPath');

  try {
    if (Platform.isWindows) {
      await _openPdfOnWindows(path, page, appName, readerPath);
    } else if (Platform.isMacOS) {
      await _openPdfOnMacOS(path, page, appName, readerPath);
    } else if (Platform.isLinux) {
      await _openPdfOnLinux(path, page, appName, readerPath);
    } else {
      logger.w('Unsupported platform for custom PDF reader, using system default');
      await openFileBySystemDefault(path);
    }
  } catch (e) {
    logger.e('Failed to handle PDF link: $e');
    // Don't rethrow - the individual platform methods handle their own fallbacks
  }
}

/// Open PDF on Windows with appropriate arguments
Future<void> _openPdfOnWindows(String path, String page, String appName, String readerPath) async {
  List<String> args = [];

  // Validate inputs
  if (path.isEmpty) {
    logger.e('PDF path is empty');
    throw ArgumentError('PDF path cannot be empty');
  }

  if (readerPath.isEmpty) {
    logger.e('PDF reader path is empty');
    throw ArgumentError('PDF reader path cannot be empty');
  }

  // Normalize path separators for Windows
  final normalizedPath = path.replaceAll('/', '\\');

  // Check if PDF file exists
  if (!File(normalizedPath).existsSync()) {
    logger.e('PDF file does not exist: $normalizedPath');
    throw FileSystemException('PDF file not found', normalizedPath);
  }

  // Check if PDF reader executable exists
  if (!File(readerPath).existsSync()) {
    logger.e('PDF reader executable does not exist: $readerPath');
    throw FileSystemException('PDF reader not found', readerPath);
  }

  if (appName.contains('foxit')) {
    args = ['/A', 'page=$page', normalizedPath];
  } else if (appName.contains('adobe')) {
    args = ['/A', 'page=$page', 'OpenActions', normalizedPath];
  } else if (appName.contains('sumatra')) {
    args = [normalizedPath, '-page', page];
  } else if (appName.contains('pdfannotator')) {
    args = ['/page=$page', normalizedPath];
  } else if (appName.contains('PDFXCview'.toLowerCase())) {
    args = ['/A', 'page=$page', normalizedPath];
  } else if (appName.contains('okular')) {
    args = ['-p', page, normalizedPath];
  } else if (appName.contains('chrome') || appName.contains('msedge') || appName.contains('firefox')) {
    args = ['file:///${Uri.encodeFull(normalizedPath.replaceAll("\\", "/"))}#page=$page'];
  } else {
    // Default fallback for unknown readers
    logger.w('Unknown PDF reader: $appName, using default arguments');
    args = [normalizedPath];
  }

  logger.i('Opening PDF with reader: $appName');
  logger.i('Command: $readerPath');
  logger.i('Arguments: $args');
  logger.i('PDF path: $normalizedPath');
  logger.i('Target page: $page');

  try {
    final result = await Process.run(readerPath, args, runInShell: true);

    if (result.exitCode == 0) {
      logger.i('PDF opened successfully');
    } else {
      logger.e('PDF reader exited with code: ${result.exitCode}');
      if (result.stderr.isNotEmpty) {
        logger.e('PDF reader stderr: ${result.stderr}');
      }
      if (result.stdout.isNotEmpty) {
        logger.i('PDF reader stdout: ${result.stdout}');
      }

      // Try fallback method if the specific reader failed
      logger.w('Attempting fallback: opening PDF with system default');
      await openFileBySystemDefault(normalizedPath);
    }
  } catch (e, stackTrace) {
    logger.e('Failed to open PDF with reader: $e');
    logger.e('Stack trace: $stackTrace');

    // Try fallback method
    logger.w('Attempting fallback: opening PDF with system default');
    try {
      await openFileBySystemDefault(normalizedPath);
      logger.i('PDF opened successfully using system default');
    } catch (fallbackError) {
      logger.e('Fallback method also failed: $fallbackError');
      rethrow;
    }
  }
}

/// Open PDF on macOS with appropriate arguments
Future<void> _openPdfOnMacOS(String path, String page, String appName, String readerPath) async {
  List<String> args;

  // Validate inputs
  if (path.isEmpty || readerPath.isEmpty) {
    logger.e('Invalid path or reader path for macOS PDF opening');
    throw ArgumentError('Path and reader path cannot be empty');
  }

  if (appName.contains('foxit')) {
    args = [path];
  } else if (appName.contains('chrome') || appName.contains('msedge') || appName.contains('firefox')) {
    if (!path.startsWith('/')) {
      args = ['file:///${Uri.encodeFull(path)}#page=$page'];
    } else {
      args = ['file://${Uri.encodeFull(path)}#page=$page'];
    }
  } else {
    args = [path];
  }

  logger.i('Opening PDF on macOS with reader: $appName');
  logger.i('Command: $readerPath');
  logger.i('Arguments: $args');

  try {
    final result = await Process.run(readerPath, args, runInShell: true);

    if (result.exitCode == 0) {
      logger.i('PDF opened successfully on macOS');
    } else {
      logger.e('PDF reader exited with code: ${result.exitCode}');
      if (result.stderr.isNotEmpty) {
        logger.e('PDF reader stderr: ${result.stderr}');
      }

      // Try fallback method
      logger.w('Attempting fallback: opening PDF with system default');
      await openFileBySystemDefault(path);
    }
  } catch (e) {
    logger.e('Failed to open PDF on macOS: $e');

    // Try fallback method
    try {
      await openFileBySystemDefault(path);
      logger.i('PDF opened successfully using system default on macOS');
    } catch (fallbackError) {
      logger.e('Fallback method also failed on macOS: $fallbackError');
      rethrow;
    }
  }
}

/// Open PDF on Linux with appropriate arguments
Future<void> _openPdfOnLinux(String path, String page, String appName, String readerPath) async {
  List<String> args;

  // Validate inputs
  if (path.isEmpty || readerPath.isEmpty) {
    logger.e('Invalid path or reader path for Linux PDF opening');
    throw ArgumentError('Path and reader path cannot be empty');
  }

  switch (appName) {
    case 'okular':
    case 'evince':
      args = ['-p', page, path];
      break;
    default:
      args = [path];
  }

  logger.i('Opening PDF on Linux with reader: $appName');
  logger.i('Command: $readerPath');
  logger.i('Arguments: $args');

  try {
    final result = await Process.run(readerPath, args, runInShell: true);

    if (result.exitCode == 0) {
      logger.i('PDF opened successfully on Linux');
    } else {
      logger.e('PDF reader exited with code: ${result.exitCode}');
      if (result.stderr.isNotEmpty) {
        logger.e('PDF reader stderr: ${result.stderr}');
      }

      // Try fallback method
      logger.w('Attempting fallback: opening PDF with system default');
      await openFileBySystemDefault(path);
    }
  } catch (e) {
    logger.e('Failed to open PDF on Linux: $e');

    // Try fallback method
    try {
      await openFileBySystemDefault(path);
      logger.i('PDF opened successfully using system default on Linux');
    } catch (fallbackError) {
      logger.e('Fallback method also failed on Linux: $fallbackError');
      rethrow;
    }
  }
}

class VideoNoteApi {
  Future<shelf.Response> _messages(shelf.Request request) async {
    return shelf.Response.ok('[]');
  }

  // By exposing a [Router] for an object, it can be mounted in other routers.
  Router get router {
    final router = Router();

    // A handler can have more that one route.
    router.get('/messages', _messages);
    router.get('/messages/', _messages);

    // This nested catch-all, will only catch /api/.* when mounted above.
    // Notice that ordering if annotated handlers and mounts is significant.
    router.all('/<ignored|.*>',
        (shelf.Request request) => shelf.Response.notFound('null'));

    return router;
  }
}

// Enhanced API Server Management System
class ApiServerManager {
  Process? _apiServerProcess;
  Timer? _healthCheckTimer;
  Timer? _startupRetryTimer;
  bool _isShuttingDown = false;
  bool _isStarting = false;
  int _startupAttempts = 0;
  static const int _maxStartupAttempts = 3;
  static const Duration _healthCheckInterval = Duration(seconds: 30);
  static const Duration _startupRetryDelay = Duration(seconds: 5);
  static const Duration _serverHealthTimeout = Duration(seconds: 5);

  // Singleton pattern for global access
  static final ApiServerManager _instance = ApiServerManager._internal();
  factory ApiServerManager() => _instance;
  ApiServerManager._internal();

  /// Get the current server process
  Process? get serverProcess => _apiServerProcess;

  /// Check if server is currently starting
  bool get isStarting => _isStarting;

  /// Check if server is shutting down
  bool get isShuttingDown => _isShuttingDown;

  /// Initialize the API server with enhanced reliability
  Future<void> _initializeServer(int port) async {
    if (_isStarting || _isShuttingDown) {
      logger.w('Server initialization skipped - already in progress or shutting down');
      return;
    }

    _isStarting = true;
    _startupAttempts = 0;

    try {
      // Check if a compatible server is already running
      if (await _isApiServerRunning(port)) {
        logger.i('Compatible API Server is already running on port $port');
        _startHealthMonitoring(port);
        return;
      }

      // Determine server executable path
      String serverPath = _getServerExecutablePath();

      // Check if server executable exists
      if (!File(serverPath).existsSync()) {
        logger.w('API Server executable not found at: $serverPath');
        await _handleMissingExecutable(port);
        return;
      }

      // Start the managed server with retry logic
      await _startManagedServerWithRetry(serverPath, port);

    } finally {
      _isStarting = false;
    }
  }

  /// Handle missing executable scenario with fallback to manual server detection
  Future<void> _handleMissingExecutable(int port) async {
    logger.i('Will wait for manual server startup or check again later');

    // Wait up to 30 seconds for a manual server to start
    for (int i = 0; i < 30; i++) {
      await Future.delayed(const Duration(seconds: 1));

      if (await _isApiServerRunning(port)) {
        logger.i('Manual API server detected and verified');
        _startHealthMonitoring(port);
        return;
      }
    }

    logger.w('No manual server detected after 30 seconds');
    logger.w('PDF processing features may not work until server is started');
  }

  /// Start managed server with retry logic and enhanced error handling
  Future<void> _startManagedServerWithRetry(String serverPath, int port) async {
    while (_startupAttempts < _maxStartupAttempts && !_isShuttingDown) {
      _startupAttempts++;

      try {
        logger.i('Starting managed API server (attempt $_startupAttempts/$_maxStartupAttempts): $serverPath');

        await _startSingleManagedServer(serverPath, port);

        // If we get here, server started successfully
        logger.i('Managed API Server started successfully on port $port');
        _startHealthMonitoring(port);
        _startupAttempts = 0; // Reset for future restarts
        return;

      } catch (e) {
        logger.e('Failed to start managed API server (attempt $_startupAttempts): $e');

        // Clean up failed process
        await _cleanupFailedProcess();

        if (_startupAttempts < _maxStartupAttempts) {
          logger.i('Retrying server startup in ${_startupRetryDelay.inSeconds} seconds...');
          await Future.delayed(_startupRetryDelay);
        }
      }
    }

    // All attempts failed
    logger.e('Failed to start managed API server after $_maxStartupAttempts attempts');
    throw Exception('API Server startup failed after $_maxStartupAttempts attempts');
  }

  /// Start a single managed server instance with enhanced monitoring
  Future<void> _startSingleManagedServer(String serverPath, int port) async {
    // Start the process with proper configuration
    _apiServerProcess = await Process.start(
      serverPath,
      ["-p", port.toString()],
      mode: ProcessStartMode.normal,
    );

    // Create process tracking file for cleanup
    await _createProcessTrackingFile(_apiServerProcess!.pid);

    // Set up process monitoring
    _setupProcessMonitoring();

    // Wait for server to start and verify it's working
    bool serverReady = false;
    for (int i = 0; i < 15; i++) { // Increased timeout to 15 seconds
      await Future.delayed(const Duration(seconds: 1));

      if (await _isApiServerRunning(port)) {
        serverReady = true;
        break;
      }

      // Check if process is still alive
      if (_apiServerProcess?.exitCode != null) {
        final exitCode = await _apiServerProcess!.exitCode;
        throw Exception('Server process exited prematurely with code: $exitCode');
      }
    }

    if (!serverReady) {
      throw Exception('Server failed to become ready within timeout period');
    }
  }

  /// Set up comprehensive process monitoring
  void _setupProcessMonitoring() {
    if (_apiServerProcess == null) return;

    // Monitor stdout for server messages
    _apiServerProcess!.stdout.transform(utf8.decoder).listen(
      (data) {
        logger.d('API Server stdout: ${data.trim()}');
      },
      onError: (error) {
        logger.e('API Server stdout error: $error');
      },
    );

    // Monitor stderr for error messages
    _apiServerProcess!.stderr.transform(utf8.decoder).listen(
      (data) {
        logger.e('API Server stderr: ${data.trim()}');
      },
      onError: (error) {
        logger.e('API Server stderr error: $error');
      },
    );

    // Monitor process exit
    _apiServerProcess!.exitCode.then((exitCode) {
      logger.w('API Server process exited with code: $exitCode');
      _apiServerProcess = null;

      // If not shutting down, attempt restart
      if (!_isShuttingDown && exitCode != 0) {
        logger.i('Attempting to restart API server after unexpected exit');
        _scheduleRestart();
      }
    });
  }

  /// Start health monitoring for the API server
  void _startHealthMonitoring(int port) {
    _stopHealthMonitoring(); // Stop any existing monitoring

    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (timer) async {
      if (_isShuttingDown) {
        timer.cancel();
        return;
      }

      try {
        final isRunning = await _isApiServerRunning(port);
        if (!isRunning) {
          logger.w('Health check failed - API server not responding');
          timer.cancel();

          // Attempt restart if we have a managed process
          if (_apiServerProcess != null) {
            _scheduleRestart();
          }
        } else {
          logger.d('Health check passed - API server is running');
        }
      } catch (e) {
        logger.e('Health check error: $e');
      }
    });

    logger.i('Health monitoring started for API server on port $port');
  }

  /// Stop health monitoring
  void _stopHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  /// Schedule a restart of the API server
  void _scheduleRestart() {
    if (_isShuttingDown || _isStarting) return;

    _startupRetryTimer?.cancel();
    _startupRetryTimer = Timer(_startupRetryDelay, () async {
      if (_isShuttingDown) return;

      try {
        final settingController = Get.find<SettingController>();
        final port = settingController.pyServerPort.value;

        logger.i('Attempting scheduled restart of API server');
        await _initializeServer(port);
      } catch (e) {
        logger.e('Scheduled restart failed: $e');
      }
    });
  }

  /// Clean up failed process
  Future<void> _cleanupFailedProcess() async {
    if (_apiServerProcess != null) {
      try {
        _apiServerProcess!.kill();
        await _apiServerProcess!.exitCode.timeout(
          const Duration(seconds: 3),
          onTimeout: () => -1,
        );
      } catch (e) {
        logger.e('Error cleaning up failed process: $e');
      } finally {
        _apiServerProcess = null;
      }
    }
  }

  /// Gracefully shutdown the API server
  Future<void> shutdown() async {
    if (_isShuttingDown) return;

    _isShuttingDown = true;
    logger.i('Shutting down API server...');

    try {
      // Stop monitoring timers
      _stopHealthMonitoring();
      _startupRetryTimer?.cancel();
      _startupRetryTimer = null;

      // Terminate managed process
      if (_apiServerProcess != null) {
        await _terminateManagedProcess();
      }

      // Ensure port is clear
      final settingController = Get.find<SettingController>();
      final port = settingController.pyServerPort.value;
      await _ensurePortIsClear(port);

      logger.i('API server shutdown completed');
    } catch (e) {
      logger.e('Error during API server shutdown: $e');
    } finally {
      _isShuttingDown = false;
    }
  }

  /// Terminate the managed process gracefully
  Future<void> _terminateManagedProcess() async {
    if (_apiServerProcess == null) return;

    try {
      logger.i('Terminating managed server process...');
      final pid = _apiServerProcess!.pid;

      if (Platform.isWindows) {
        // Windows platform - enhanced termination with proper waiting
        bool killed = _apiServerProcess!.kill();

        // Wait for graceful termination first
        bool gracefullyTerminated = false;
        try {
          await _apiServerProcess!.exitCode.timeout(
            const Duration(seconds: 2),
            onTimeout: () => -1,
          );
          gracefullyTerminated = true;
          logger.i('Server process terminated gracefully');
        } catch (e) {
          logger.w('Graceful termination timeout, forcing kill...');
        }

        // If graceful termination failed, force kill
        if (!gracefullyTerminated || !killed) {
          try {
            final result = await Process.run(
              'taskkill',
              ['/F', '/PID', '$pid'],
              runInShell: true
            );
            logger.i('Force killed server process (PID: $pid): ${result.stdout}');

            // Wait a moment for the force kill to take effect
            await Future.delayed(const Duration(milliseconds: 500));
          } catch (e) {
            logger.e('Failed to force kill process $pid: $e');
          }
        }

        // Remove process tracking file
        await _removeProcessTrackingFile(pid);
      } else {
        // Unix-like platforms
        if (!_apiServerProcess!.kill()) {
          _apiServerProcess!.kill(ProcessSignal.sigkill);
        }

        // Wait for process to terminate
        await _apiServerProcess!.exitCode.timeout(
          const Duration(seconds: 3),
          onTimeout: () {
            _apiServerProcess!.kill(ProcessSignal.sigkill);
            return -1;
          },
        );
      }

      logger.i('Managed server process terminated');
    } catch (e) {
      logger.e('Error terminating managed server: $e');
    } finally {
      _apiServerProcess = null;
    }
  }

  /// Ensure the specified port is clear of any processes
  Future<void> _ensurePortIsClear(int port) async {
    try {
      logger.d('Ensuring port $port is clear...');

      if (Platform.isWindows) {
        // Kill any process using the port on Windows
        await Process.run(
          'cmd.exe',
          ['/c', 'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'],
          runInShell: true,
        );
      } else {
        // Kill any process using the port on Unix-like systems
        try {
          final result = await Process.run('lsof', ['-ti:$port']);
          if (result.stdout.toString().trim().isNotEmpty) {
            final pids = result.stdout.toString().trim().split('\n');
            for (final pid in pids) {
              if (pid.isNotEmpty) {
                await Process.run('kill', ['-9', pid]);
              }
            }
          }
        } catch (e) {
          // lsof might not be available, ignore
          logger.d('lsof not available or no processes found: $e');
        }
      }

      logger.d('Port $port cleanup completed');
    } catch (e) {
      logger.w('Error clearing port $port: $e');
    }
  }

  /// Get comprehensive server status information
  Map<String, dynamic> getServerStatus() {
    final settingController = Get.find<SettingController>();
    final port = settingController.pyServerPort.value;

    return {
      'timestamp': DateTime.now().toIso8601String(),
      'server_process_active': _apiServerProcess != null,
      'server_process_pid': _apiServerProcess?.pid,
      'is_starting': _isStarting,
      'is_shutting_down': _isShuttingDown,
      'startup_attempts': _startupAttempts,
      'max_startup_attempts': _maxStartupAttempts,
      'health_monitoring_active': _healthCheckTimer?.isActive ?? false,
      'port': port,
      'server_executable_path': _getServerExecutablePath(),
      'server_executable_exists': File(_getServerExecutablePath()).existsSync(),
    };
  }

  /// Force restart the API server (for debugging/recovery)
  Future<void> forceRestart() async {
    if (_isStarting) {
      logger.w('Cannot force restart - server is already starting');
      return;
    }

    logger.i('Force restarting API server...');

    try {
      // Stop current instance
      await shutdown();

      // Wait a moment for cleanup
      await Future.delayed(const Duration(seconds: 2));

      // Restart
      final settingController = Get.find<SettingController>();
      final port = settingController.pyServerPort.value;
      await _initializeServer(port);

      logger.i('Force restart completed');
    } catch (e) {
      logger.e('Force restart failed: $e');
      rethrow;
    }
  }



  /// Create a process tracking file for cleanup purposes
  Future<void> _createProcessTrackingFile(int pid) async {
    try {
      if (Platform.isWindows) {
        final tempDir = Directory.systemTemp;
        final trackingFile = File('${tempDir.path}/anki_guru_server_$pid.pid');
        await trackingFile.writeAsString('$pid\n${DateTime.now().toIso8601String()}');
        logger.d('Created process tracking file: ${trackingFile.path}');
      }
    } catch (e) {
      logger.w('Failed to create process tracking file: $e');
    }
  }

  /// Remove process tracking file
  Future<void> _removeProcessTrackingFile(int pid) async {
    try {
      if (Platform.isWindows) {
        final tempDir = Directory.systemTemp;
        final trackingFile = File('${tempDir.path}/anki_guru_server_$pid.pid');
        if (await trackingFile.exists()) {
          await trackingFile.delete();
          logger.d('Removed process tracking file: ${trackingFile.path}');
        }
      }
    } catch (e) {
      logger.w('Failed to remove process tracking file: $e');
    }
  }

  /// Clean up orphaned server processes using tracking files
  static Future<void> cleanupOrphanedProcesses() async {
    try {
      if (Platform.isWindows) {
        final tempDir = Directory.systemTemp;
        final trackingFiles = tempDir
            .listSync()
            .whereType<File>()
            .where((file) => file.path.contains('anki_guru_server_') && file.path.endsWith('.pid'));

        for (final file in trackingFiles) {
          try {
            final content = await file.readAsString();
            final lines = content.split('\n');
            if (lines.isNotEmpty) {
              final pid = int.parse(lines[0]);

              // Check if process is still running
              final result = Process.runSync(
                'tasklist',
                ['/FI', 'PID eq $pid'],
                runInShell: true,
              );

              if (result.stdout.toString().contains('$pid')) {
                // Process is still running, kill it
                logger.i('Cleaning up orphaned server process: $pid');
                Process.runSync('taskkill', ['/F', '/PID', '$pid'], runInShell: true);
              }

              // Remove the tracking file
              await file.delete();
            }
          } catch (e) {
            logger.w('Error processing tracking file ${file.path}: $e');
            // Try to delete the file anyway
            try {
              await file.delete();
            } catch (_) {}
          }
        }
      }
    } catch (e) {
      logger.e('Error during orphaned process cleanup: $e');
    }
  }
}

// Global instance for backward compatibility
HttpServer? _httpServer;

/// Initialize API Server with enhanced reliability and process management
Future<void> initApiServer() async {
  if (Platform.isIOS || Platform.isAndroid) {
    return;
  }

  // Clean up any orphaned processes from previous runs
  await ApiServerManager.cleanupOrphanedProcesses();

  final manager = ApiServerManager();

  // Prevent concurrent initialization
  if (manager.isStarting) {
    logger.i('API Server initialization already in progress');
    return;
  }

  final settingController = Get.find<SettingController>();
  final port = settingController.pyServerPort.value;

  logger.i('Initializing Enhanced API Server on port $port');

  try {
    await manager._initializeServer(port);
  } catch (e) {
    logger.e('Failed to initialize API Server: $e');
    // Don't rethrow - let the application continue without the API server
  }
}

/// Enhanced API server health check with comprehensive error handling
Future<bool> _isApiServerRunning(int port) async {
  try {
    // Test basic connectivity with configurable timeout
    const timeout = ApiServerManager._serverHealthTimeout;

    final response = await http.get(
      Uri.parse('http://127.0.0.1:$port/'),
      headers: {
        'User-Agent': 'AnkiGuru-HealthCheck/1.0',
        'Accept': 'application/json',
      },
    ).timeout(timeout);

    // Check HTTP status
    if (response.statusCode != 200) {
      logger.d('API Server health check failed - HTTP ${response.statusCode}');
      return false;
    }

    // Verify it's our API server by checking the response format
    try {
      final data = jsonDecode(response.body);
      final message = data['message']?.toString() ?? '';
      final status = data['status']?.toString() ?? '';

      if (message.contains('PDF Guru API') && status == 'ok') {
        logger.d('Verified compatible API server is running on port $port');
        return true;
      } else {
        logger.d('Server response format not recognized - message: $message, status: $status');
        return false;
      }
    } catch (e) {
      logger.d('Failed to parse server response as JSON: $e');
      return false;
    }

  } on TimeoutException catch (e) {
    logger.d('API Server health check timed out: $e');
    return false;
  } on SocketException catch (e) {
    logger.d('API Server connection failed: $e');
    return false;
  } on HttpException catch (e) {
    logger.d('API Server HTTP error: $e');
    return false;
  } catch (e) {
    logger.d('API Server health check failed with unexpected error: $e');
    return false;
  }
}

/// Get the expected server executable path for the current platform
String _getServerExecutablePath() {
  if (Platform.isMacOS) {
    return "/Library/Application Support/PDF Guru Anki2/server/server";
  } else if (Platform.isWindows) {
    String appPath = Platform.resolvedExecutable;
    return PathUtils.join([PathUtils(appPath).parent, 'server', 'server.exe']);
  } else if (Platform.isLinux) {
    String appPath = Platform.resolvedExecutable;
    return PathUtils.join([PathUtils(appPath).parent, 'server', 'server']);
  }
  return "";
}

// Updated termination functions to use the new ApiServerManager

/// Terminate API server using the enhanced manager
Future<void> terminateApiServer() async {
  final manager = ApiServerManager();
  await manager.shutdown();
}

/// Get API server status for external monitoring
Map<String, dynamic> getApiServerStatus() {
  final manager = ApiServerManager();
  return manager.getServerStatus();
}

/// Force restart API server (for debugging/recovery)
Future<void> forceRestartApiServer() async {
  final manager = ApiServerManager();
  await manager.forceRestart();
}

/// Check if API server is healthy
Future<bool> isApiServerHealthy() async {
  final settingController = Get.find<SettingController>();
  final port = settingController.pyServerPort.value;
  return await _isApiServerRunning(port);
}

// 初始化HTTP服务器
Future<void> initHttpServer() async {
  try {
    final settingController = Get.find<SettingController>();
    final guruServiceRouter = Router();

    // 现有的HTTP路由
    guruServiceRouter.get("/", (shelf.Request request) {
      return shelf.Response.ok('Welcome to PDF Guru Anki API Server');
    });
    guruServiceRouter.get("/status", (shelf.Request request) {
      return shelf.Response.ok(
        jsonEncode({
          'status': 'running',
          'timestamp': DateTime.now().toIso8601String()
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/version", (shelf.Request request) async {
      final packageInfo = await PackageInfo.fromPlatform();
      return shelf.Response.ok(
        jsonEncode({
          'version': packageInfo.version,
          'buildNumber': packageInfo.buildNumber,
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/screenshot_card", (shelf.Request request) async {
      final imageCardController = Get.find<ImageCardController>();
      final currentRoute = Get.currentRoute;
      const targetRoute = '/image_card';
      if (currentRoute != targetRoute) {
        await Get.toNamed(targetRoute);
      }
      await imageCardController.loadImageFromClipboard();
      return shelf.Response.ok(
        jsonEncode({
          'status': 'success',
        }),
        headers: {'content-type': 'application/json'},
      );
    });
    guruServiceRouter.get("/guru_jump", (shelf.Request request) async {
      final url = request.url.queryParameters['url'] ??
          request.url.queryParameters['uri'];
      if (url == null) {
        return shelf.Response.badRequest(
          body: jsonEncode({'error': 'Missing url or uri parameter'}),
          headers: {'content-type': 'application/json'},
        );
      }
      logger.d('Processing guru_jump GET with url: $url');
      final rawUrl = Uri.decodeComponent(url); // 先进行URL解码
      processLink(Uri.parse(rawUrl));

      return shelf.Response.ok(
        '''
        <!DOCTYPE html>
        <html>
        <head>
          <script>
            window.close();
          </script>
        </head>
        <body>
        </body>
        </html>
        ''',
        headers: {'content-type': 'text/html; charset=utf-8'},
      );
    });
    guruServiceRouter.post("/guru_jump", (shelf.Request request) async {
      final contentType = request.headers['content-type'];
      String? urlParam;

      // 处理 JSON 格式的请求体
      if (contentType?.contains('application/json') == true) {
        final requestBody = await request.readAsString();
        final jsonBody = jsonDecode(requestBody);
        urlParam = jsonBody['url'] ?? jsonBody['uri'];
        if (urlParam == null) {
          return shelf.Response.badRequest(
            body: jsonEncode(
                {'error': 'Missing url or uri parameter in JSON body'}),
            headers: {'content-type': 'application/json'},
          );
        }
      }
      // 处理表单格式的请求体
      else if (contentType?.contains('application/x-www-form-urlencoded') ==
          true) {
        final formData = await request.readAsString();
        final params = Uri.splitQueryString(formData);
        urlParam = params['url'] ?? params['uri'];
        if (urlParam == null) {
          return shelf.Response.badRequest(
            body: jsonEncode(
                {'error': 'Missing url or uri parameter in form data'}),
            headers: {'content-type': 'application/json'},
          );
        }
      } else {
        return shelf.Response.badRequest(
          body: jsonEncode({'error': 'Unsupported content type'}),
          headers: {'content-type': 'application/json'},
        );
      }

      logger.d('Processing guru_jump POST with url: $urlParam');
      final rawUrl = Uri.decodeComponent(urlParam); // 先进行URL解码
      processLink(Uri.parse(rawUrl));

      return shelf.Response.ok(
        jsonEncode({'status': 'success'}),
        headers: {'content-type': 'application/json'},
      );
    });

    // 添加WebSocket支持
    guruServiceRouter.get('/connect', (shelf.Request request) {
      final timestamp = DateTime.now().toIso8601String();
      final clientIP = request.headers['x-forwarded-for'] ??
                      request.headers['x-real-ip'] ??
                      'localhost';

      logger.i('[$timestamp] WebSocket连接请求 - 客户端IP: $clientIP, User-Agent: ${request.headers['user-agent']}');

      return shelf_web_socket.webSocketHandler((webSocket, String? protocol) {
        final connectionTimestamp = DateTime.now().toIso8601String();
        // 连接建立
        logger.i('[$connectionTimestamp] WebSocket连接已建立 - 客户端: $clientIP, 协议: $protocol');

        // 注册WebSocket连接到管理器
        final wsManager = Get.find<WebSocketManager>();
        wsManager.addConnection(webSocket);

        logger.i('[$connectionTimestamp] WebSocket连接已注册到管理器，当前连接数: ${wsManager.connectionCount.value}');

        // 处理接收到的消息
        webSocket.stream.listen((message) {
          logger.d('收到WebSocket消息: $message');
          try {
            final data = jsonDecode(message);
            _handleWebSocketMessage(data, webSocket);

          } catch (e) {
            logger.e('处理WebSocket消息时出错: $e');
            webSocket.sink.add(jsonEncode(_createWebSocketResponse(
              status: 'error',
              action: 'parse_error',
              message: 'Failed to parse message',
            )));
          }
        }, onError: (error) {
          final errorTimestamp = _generateTimestamp();
          logger.e('[$errorTimestamp] WebSocket连接错误: $error');
        }, onDone: () {
          final disconnectTimestamp = _generateTimestamp();
          logger.i('[$disconnectTimestamp] WebSocket连接已断开');
          wsManager.removeConnection(webSocket);
        });
      })(request);
    });

    guruServiceRouter.all('/<ignored|.*>',
        (shelf.Request request) => shelf.Response.notFound('null'));

    // 配置CORS头部
    final corsHeadersOptions = {
      ACCESS_CONTROL_ALLOW_ORIGIN: '*',
      ACCESS_CONTROL_ALLOW_METHODS: 'GET, POST, OPTIONS',
      ACCESS_CONTROL_ALLOW_HEADERS:
          'Origin, Content-Type, Accept, Authorization',
      ACCESS_CONTROL_ALLOW_CREDENTIALS: 'true',
      ACCESS_CONTROL_MAX_AGE: '86400',
    };

    // 创建请求处理管道
    final app = const shelf.Pipeline()
        .addMiddleware(corsHeaders(headers: corsHeadersOptions))
        .addMiddleware(shelf.logRequests())
        .addHandler(guruServiceRouter.call);

    final serverPort = settingController.serverPort.value;
    final timestamp = DateTime.now().toIso8601String();

    logger.i('[$timestamp] 启动HTTP/WebSocket服务器 - 端口: $serverPort');

    _httpServer = await shelf_io.serve(app, 'localhost', serverPort);

    // 启用内容压缩
    _httpServer!.autoCompress = true;

    logger.i('[$timestamp] HTTP/WebSocket服务器启动成功！');
    logger.i('[$timestamp] 服务器地址: http://${_httpServer!.address.host}:${_httpServer!.port}');
    logger.i('[$timestamp] WebSocket端点: ws://${_httpServer!.address.host}:${_httpServer!.port}/connect');
    logger.i('[$timestamp] 等待浏览器扩展连接...');
  } catch (e, stackTrace) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 初始化HTTP/WebSocket服务器时出错: $e');
    logger.e('[$timestamp] 错误堆栈: $stackTrace');

    // 检查是否是端口被占用的错误
    if (e.toString().contains('Address already in use') ||
        e.toString().contains('port') ||
        e.toString().contains('bind')) {
      final settingController = Get.find<SettingController>();
      logger.e('[$timestamp] 端口 ${settingController.serverPort.value} 可能已被占用');
      logger.e('[$timestamp] 请检查是否有其他应用程序正在使用此端口');
      logger.e('[$timestamp] 或者尝试更改设置中的服务器端口');
    }
  }
}

// ============================================================================
// WEBSOCKET MESSAGE HANDLERS
// ============================================================================

/// Handle WebSocket message processing
void _handleWebSocketMessage(Map<String, dynamic> data, dynamic webSocket) {
  final messageType = data['type'];

  switch (messageType) {
    case 'guru_jump':
      _handleGuruJumpMessage(data, webSocket);
      break;
    case 'video_screenshot':
      _handleVideoScreenshotMessage(data, webSocket);
      break;
    case 'video_timestamp':
      _handleVideoTimestampMessage(data, webSocket);
      break;
    case 'report':
      _handleReportMessage(data, webSocket);
      break;
    case 'timestamp_link':
      _handleTimestampLinkMessage(data, webSocket);
      break;
    case 'screenshot_card':
      _handleScreenshotCardMessage(webSocket);
      break;
    case 'identify_response':
      _handleIdentifyResponseMessage(data, webSocket);
      break;
    case 'browser_control':
      _handleBrowserControl(data, webSocket);
      break;
    default:
      logger.w('收到未知消息类型: $messageType');
      webSocket.sink.add(jsonEncode(_createWebSocketResponse(
        status: 'error',
        action: 'unknown_command',
        message: 'Unknown command',
      )));
  }
}

/// Handle guru_jump message
void _handleGuruJumpMessage(Map<String, dynamic> data, dynamic webSocket) {
  if (data['url'] != null) {
    final rawUrl = Uri.decodeComponent(data['url']);
    processLink(Uri.parse(rawUrl));
    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'success',
      action: 'guru_jump',
    )));
  }
}

/// Handle video screenshot message (legacy format)
void _handleVideoScreenshotMessage(Map<String, dynamic> data, dynamic webSocket) {
  if (data['image'] != null) {
    final imageData = data['image'];
    final videoNoteController = Get.find<VideoNoteController>();
    videoNoteController.saveScreenshotFromBase64(imageData);
    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'success',
      action: 'video_screenshot',
      message: 'service.screenshot.saved'.tr,
    )));
  }
}

/// Handle video timestamp message (legacy format)
void _handleVideoTimestampMessage(Map<String, dynamic> data, dynamic webSocket) {
  if (data['timestamp'] != null) {
    final videoNoteController = Get.find<VideoNoteController>();
    final clipboardController = Get.find<ClipboardController>();

    final timestamp = data['timestamp'];
    final videoUrl = data['url'] ?? '';
    final linkText = videoNoteController.buildTimestampLink(timestamp, videoUrl);

    clipboardController.copyText(linkText);
    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'success',
      action: 'video_timestamp',
      message: 'service.timestamp.linkCopiedToClipboard'.tr,
    )));

    if (videoNoteController.isAutoPaste.value) {
      AnkiConnectController().simulatePaste();
    }
  }
}

/// Handle unified report message
void _handleReportMessage(Map<String, dynamic> data, dynamic webSocket) {
  final reportType = data['reportType'] ?? '';
  final status = data['status'] ?? '';
  final action = data['action'] ?? '';
  final reportData = data['data'] ?? {};
  final message = data['message'] ?? '';

  logger.i('收到统一报告: reportType=$reportType, status=$status, action=$action');

  switch (reportType) {
    case 'browser_navigation':
      _handleBrowserNavigationReport(status, action, reportData, message);
      break;
    case 'screenshot':
      _handleScreenshotReport(status, action, reportData, message);
      break;
    case 'timestamp':
      _handleTimestampReport(status, action, reportData, message);
      break;
    case 'video_info':
      _handleVideoInfoReport(status, action, reportData, message);
      break;
    default:
      logger.w('未知的报告类型: $reportType');
  }
}

/// Handle timestamp link message
void _handleTimestampLinkMessage(Map<String, dynamic> data, dynamic webSocket) {
  if (data['link'] != null) {
    final link = data['link'];
    final clipboardController = Get.find<ClipboardController>();
    clipboardController.copyText(link);

    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'success',
      action: 'timestamp_link',
      message: 'service.timestamp.linkCopiedToClipboard'.tr,
      additionalData: {'link': link},
    )));

    Get.snackbar(
      'service.timestamp.linkCopied'.tr,
      'service.timestamp.linkSaved'.tr,
      duration: const Duration(seconds: 2),
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

/// Handle screenshot card message
void _handleScreenshotCardMessage(dynamic webSocket) {
  _handleScreenshotCard().then((_) {
    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'success',
      action: 'screenshot_card',
    )));
  }).catchError((error) {
    webSocket.sink.add(jsonEncode(_createWebSocketResponse(
      status: 'error',
      action: 'screenshot_card',
      message: error.toString(),
    )));
  });
}

/// Handle identify response message
void _handleIdentifyResponseMessage(Map<String, dynamic> data, dynamic webSocket) {
  final timestamp = _generateTimestamp();
  logger.i('[$timestamp] 收到浏览器扩展识别响应: ${data['source']}');

  final wsManager = Get.find<WebSocketManager>();
  wsManager.confirmBrowserExtensionConnection(webSocket, data);

  webSocket.sink.add(jsonEncode(_createWebSocketResponse(
    status: 'success',
    action: 'identify_response',
    message: 'service.browser.extensionIdentified'.tr,
  )));
}

// 辅助方法处理截图卡片
Future<void> _handleScreenshotCard() async {
  final imageCardController = Get.find<ImageCardController>();
  await _navigateToRouteIfNeeded('/image_card');
  await imageCardController.loadImageFromClipboard();
}

// 处理浏览器控制命令
void _handleBrowserControl(Map<String, dynamic> data, dynamic webSocket) {
  try {
    final command = data['command'];
    logger.i('处理浏览器控制命令: $command');

    // 获取WebSocket管理器并广播命令到浏览器扩展
    try {
      final wsManager = Get.find<WebSocketManager>();
      wsManager.sendToBrowserExtension(data);

      webSocket.sink.add(jsonEncode({
        'status': 'success',
        'action': 'browser_control',
        'command': command,
        'message': 'service.browser.commandSent'.tr
      }));
    } catch (e) {
      logger.e('发送浏览器控制命令失败: $e');
      webSocket.sink.add(jsonEncode({
        'status': 'error',
        'action': 'browser_control',
        'message': 'service.browser.cannotConnect'.tr
      }));
    }
  } catch (e) {
    logger.e('处理浏览器控制命令时出错: $e');
    webSocket.sink.add(jsonEncode({
      'status': 'error',
      'action': 'browser_control',
      'message': e.toString()
    }));
  }
}

/// 检查WebSocket服务器是否正在运行
Future<bool> isWebSocketServerRunning() async {
  try {
    final settingController = Get.find<SettingController>();
    final port = settingController.serverPort.value;
    final timestamp = DateTime.now().toIso8601String();

    logger.i('[$timestamp] 检查WebSocket服务器状态 - 端口: $port');

    // 检查HTTP服务器是否存在
    if (_httpServer == null) {
      logger.w('[$timestamp] HTTP服务器实例为null');
      return false;
    }

    // 尝试连接到状态端点
    final response = await http.get(
      Uri.parse('http://localhost:$port/status'),
    ).timeout(const Duration(seconds: 3));

    if (response.statusCode == 200) {
      logger.i('[$timestamp] WebSocket服务器正在运行 - 状态码: ${response.statusCode}');
      return true;
    } else {
      logger.w('[$timestamp] WebSocket服务器响应异常 - 状态码: ${response.statusCode}');
      return false;
    }
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 检查WebSocket服务器状态失败: $e');
    return false;
  }
}

/// 获取WebSocket服务器详细状态
Map<String, dynamic> getWebSocketServerStatus() {
  try {
    final settingController = Get.find<SettingController>();
    final wsManager = Get.find<WebSocketManager>();
    final timestamp = DateTime.now().toIso8601String();

    final status = {
      'timestamp': timestamp,
      'server_running': _httpServer != null,
      'server_port': settingController.serverPort.value,
      'server_address': _httpServer?.address.toString(),
      'websocket_connections': wsManager.connectionCount.value,
      'browser_extensions': wsManager.getConnectionInfo(),
    };

    logger.d('[$timestamp] WebSocket服务器状态: $status');
    return status;
  } catch (e) {
    final timestamp = DateTime.now().toIso8601String();
    logger.e('[$timestamp] 获取WebSocket服务器状态失败: $e');
    return {
      'timestamp': timestamp,
      'error': e.toString(),
      'server_running': false,
    };
  }
}

// 终止HTTP服务器
Future<void> terminateHttpServer() async {
  if (_httpServer != null) {
    logger.i('正在终止HTTP/WebSocket服务器...');
    try {
      final settingController = Get.find<SettingController>();
      final int port = settingController.serverPort.value;

      await _httpServer!.close(force: true).timeout(const Duration(seconds: 3),
          onTimeout: () {
        logger.w('等待HTTP/WebSocket服务器关闭超时');
        return;
      });

      // 在Windows平台上，额外确保端口被释放
      if (Platform.isWindows) {
        logger.i('Windows平台：额外检查端口 $port 是否被释放');
        try {
          // 使用taskkill命令终止占用指定端口的进程
          final result = await Process.run(
            'cmd.exe',
            [
              '/c',
              'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'
            ],
            runInShell: true,
          );

          if (result.exitCode == 0 && result.stdout.isNotEmpty) {
            logger.i('端口 $port 已被强制释放: ${result.stdout}');
          }
        } catch (e) {
          logger.e('尝试释放端口 $port 时出错: $e');
        }
      }

      logger.i('HTTP/WebSocket服务器已终止');
    } catch (e) {
      logger.e('终止HTTP/WebSocket服务器时出错: $e');
    } finally {
      // 确保即使出错也将服务器引用设置为null
      _httpServer = null;
    }
  }
}

// 终止所有服务
Future<void> terminateAllServices() async {
  logger.i('正在终止所有服务...');
  try {
    // 使用Future.wait同时终止所有服务，提高效率
    await Future.wait([
      terminateApiServer(),
      terminateHttpServer(),
    ]);
    logger.i('所有服务已成功终止');

    // 在Windows平台上，进行额外的端口检查和清理
    if (Platform.isWindows) {
      final settingController = Get.find<SettingController>();
      final ports = [
        settingController.serverPort.value,
        settingController.pyServerPort.value
      ];

      logger.i('Windows平台：额外检查端口 $ports 是否被释放');

      // 延迟一小段时间，确保之前的终止操作有时间生效
      await Future.delayed(const Duration(milliseconds: 500));

      for (final port in ports) {
        try {
          // 使用同步方法检查并终止任何仍然占用这些端口的进程
          final result = Process.runSync(
            'cmd.exe',
            [
              '/c',
              'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :$port\') do taskkill /F /PID %a'
            ],
            runInShell: true,
          );

          if (result.exitCode == 0 && result.stdout.isNotEmpty) {
            logger.i('端口 $port 已被强制释放: ${result.stdout}');
          }
        } catch (e) {
          logger.e('尝试释放端口 $port 时出错: $e');
        }
      }

      // 尝试终止可能的Python进程
      try {
        final result = Process.runSync(
          'taskkill',
          ['/F', '/IM', 'python.exe'],
          runInShell: true,
        );
        logger.i('已尝试终止所有Python进程: ${result.stdout}');
      } catch (e) {
        logger.e('尝试终止Python进程时出错: $e');
      }
    }
  } catch (e) {
    logger.e('终止服务时出错: $e');
    // 确保即使出现错误，也尝试单独终止每个服务
    try {
      await terminateApiServer();
    } catch (e) {
      logger.e('终止API服务器时出错: $e');
    }

    try {
      await terminateHttpServer();
    } catch (e) {
      logger.e('终止HTTP服务器时出错: $e');
    }
  }
}

/// 处理浏览器导航报告
void _handleBrowserNavigationReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'open_video_with_timestamp') {
      showLocalNotification(
        'service.video.openedInBrowser'.tr,
        message.isNotEmpty ? message : 'service.video.openedAndJumped'.tr
      );
    } else if (action == 'seek_to_timestamp') {
      showLocalNotification(
        'service.video.jumpedToTime'.tr,
        message.isNotEmpty ? message : 'service.video.jumpedToTimestamp'.tr
      );
    }
    logger.i('浏览器导航成功: $action, 数据: $data');
  } else if (status == 'error') {
    showLocalNotification(
      'service.browser.navigationFailed'.tr,
      message.isNotEmpty ? message : 'service.browser.cannotOpenVideo'.tr
    );
    logger.e('浏览器导航失败: $action, 错误: $message');
  }
}

/// 处理截图报告
void _handleScreenshotReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'take_screenshot') {
      final videoNoteController = Get.find<VideoNoteController>();
      final base64Image = data['image'] ?? '';

      if (base64Image.isNotEmpty) {
        // 处理截图数据 (使用现有的截图处理逻辑)
        videoNoteController.saveScreenshotFromBase64(base64Image);
        logger.i('截图数据处理成功');
      } else {
        logger.w('截图数据为空');
        showLocalNotification(
          'service.screenshot.failed'.tr,
          'service.screenshot.dataEmpty'.tr
        );
      }
    }
  } else if (status == 'error') {
    showLocalNotification(
      'service.screenshot.failed'.tr,
      message.isNotEmpty ? message : 'service.screenshot.browserFailed'.tr
    );
    logger.e('截图失败: $action, 错误: $message');
  }
}

/// 处理时间戳报告
void _handleTimestampReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    if (action == 'generate_timestamp_link') {
      final videoNoteController = Get.find<VideoNoteController>();
      final clipboardController = Get.find<ClipboardController>();

      final videoUrl = data['url'] ?? '';
      final timestamp = data['timestamp'] ?? '';
      final title = data['title'] ?? '';

      logger.i('收到浏览器扩展时间戳数据: URL=$videoUrl, 时间=$timestamp, 标题=$title');

      // 使用与本地视频相同的逻辑生成时间戳链接
      final linkText = videoNoteController.buildTimestampLink(timestamp, videoUrl);

      clipboardController.copyText(linkText);

      // 自动粘贴功能 (与本地视频保持一致)
      if (videoNoteController.isAutoPaste.value) {
        AnkiConnectController().simulatePaste();
      }

      showLocalNotification(
        'service.timestamp.linkCopied'.tr,
        message.isNotEmpty ? message : 'service.timestamp.linkCopiedToClipboard'.tr
      );
    }
  } else if (status == 'error') {
    showLocalNotification(
      'service.timestamp.generationFailed'.tr,
      message.isNotEmpty ? message : 'service.timestamp.browserExtensionFailed'.tr
    );
    logger.e('时间戳生成失败: $action, 错误: $message');
  }
}

/// 处理视频信息报告
void _handleVideoInfoReport(String status, String action, Map<String, dynamic> data, String message) {
  if (status == 'success') {
    logger.i('视频信息获取成功: $action, 数据: $data');
    // 可以在这里处理视频信息，比如显示视频标题、时长等
  } else if (status == 'error') {
    showLocalNotification(
      'service.video.infoRetrievalFailed'.tr,
      message.isNotEmpty ? message : 'service.video.cannotRetrieveInfo'.tr
    );
    logger.e('视频信息获取失败: $action, 错误: $message');
  }
}
